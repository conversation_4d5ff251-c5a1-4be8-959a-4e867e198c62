
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Briefcase, Calendar, TrendingUp, Sparkles, Loader2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useCandidates } from "@/hooks/useCandidates";
import { useJobs } from "@/hooks/useJobs";
import { useDataInvalidation } from "@/hooks/useGlobalInvalidation";
import { useState, useEffect } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export function DashboardStats() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [aiInsights, setAiInsights] = useState<string>("");
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);
  const [trendData, setTrendData] = useState({
    candidatesTrend: 0,
    jobsTrend: 0,
    weeklyActivity: 0,
    weeklyTrend: 0,
    scoreTrend: 0
  });

  // Set up global invalidation for cross-component consistency
  useDataInvalidation(
    ['candidates', 'jobs'],
    (changedType, event) => {
      console.log(`📊 Dashboard refreshing due to ${changedType} change:`, event.reason);
      // The unified hooks will automatically update via real-time subscriptions
    },
    { debounceMs: 200 }
  );

  // Use unified real-time hooks
  const { data: candidates = [], isLoading: candidatesLoading } = useCandidates();
  const { data: jobs = [], isLoading: jobsLoading } = useJobs();

  const isLoading = candidatesLoading || jobsLoading;

  // Calculate trend data
  useEffect(() => {
    const calculateTrends = async () => {
      if (!user) return;

      try {
        // Calculate candidates trend (last 30 days vs previous 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

        const { data: recentCandidates } = await supabase
          .from('candidates')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', thirtyDaysAgo.toISOString());

        const { data: previousCandidates } = await supabase
          .from('candidates')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', sixtyDaysAgo.toISOString())
          .lt('created_at', thirtyDaysAgo.toISOString());

        const recentCount = recentCandidates?.length || 0;
        const previousCount = previousCandidates?.length || 0;
        const candidatesTrend = previousCount > 0 ? ((recentCount - previousCount) / previousCount) * 100 : 0;

        // Calculate jobs trend (active jobs created in last 30 days vs previous 30 days)
        const { data: recentJobs } = await supabase
          .from('jobs')
          .select('id')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .gte('created_at', thirtyDaysAgo.toISOString());

        const { data: previousJobs } = await supabase
          .from('jobs')
          .select('id')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .gte('created_at', sixtyDaysAgo.toISOString())
          .lt('created_at', thirtyDaysAgo.toISOString());

        const recentJobsCount = recentJobs?.length || 0;
        const previousJobsCount = previousJobs?.length || 0;
        const jobsTrend = previousJobsCount > 0 ? ((recentJobsCount - previousJobsCount) / previousJobsCount) * 100 : 0;

        // Calculate weekly activity (candidate timeline events this week)
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const twoWeeksAgo = new Date();
        twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

        const { data: thisWeekActivity } = await supabase
          .from('candidate_timeline')
          .select('id')
          .eq('user_id', user.id)
          .gte('event_date', oneWeekAgo.toISOString());

        const { data: lastWeekActivity } = await supabase
          .from('candidate_timeline')
          .select('id')
          .eq('user_id', user.id)
          .gte('event_date', twoWeeksAgo.toISOString())
          .lt('event_date', oneWeekAgo.toISOString());

        const thisWeekCount = thisWeekActivity?.length || 0;
        const lastWeekCount = lastWeekActivity?.length || 0;
        const weeklyTrend = lastWeekCount > 0 ? ((thisWeekCount - lastWeekCount) / lastWeekCount) * 100 : 0;

        // Calculate relationship score trend
        const currentAvgScore = candidates.length
          ? candidates.reduce((acc, c) => acc + (c.relationshipScore || 0), 0) / candidates.length
          : 0;

        // Get previous period candidates for score comparison
        const { data: previousPeriodCandidates } = await supabase
          .from('candidates')
          .select('relationship_score')
          .eq('user_id', user.id)
          .gte('created_at', sixtyDaysAgo.toISOString())
          .lt('created_at', thirtyDaysAgo.toISOString());

        const previousAvgScore = previousPeriodCandidates?.length
          ? previousPeriodCandidates.reduce((acc, c) => acc + (c.relationship_score || 0), 0) / previousPeriodCandidates.length
          : 0;

        const scoreTrend = previousAvgScore > 0 ? ((currentAvgScore - previousAvgScore) / previousAvgScore) * 100 : 0;

        setTrendData({
          candidatesTrend,
          jobsTrend,
          weeklyActivity: thisWeekCount,
          weeklyTrend,
          scoreTrend
        });

      } catch (error) {
        console.error('Error calculating trends:', error);
      }
    };

    calculateTrends();
  }, [user, candidates, jobs]);

  const generateAIInsights = async () => {
    if (isGeneratingInsights || isLoading) return;

    setIsGeneratingInsights(true);
    try {
      const dashboardData = {
        totalCandidates: candidates.length,
        totalJobs: jobs.length,
        activeJobs: jobs.filter(job => job.status === 'active').length,
        recentCandidates: candidates.filter(c => {
          const createdAt = new Date(c.createdAt);
          const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          return createdAt > weekAgo;
        }).length
      };

      const prompt = `
Analyze the following recruitment dashboard data and provide strategic insights:

Dashboard Metrics:
- Total Candidates: ${dashboardData.totalCandidates}
- Total Jobs: ${dashboardData.totalJobs}
- Active Jobs: ${dashboardData.activeJobs}
- New Candidates (Last 7 days): ${dashboardData.recentCandidates}

Provide insights including:
1. Overall recruitment health assessment
2. Key trends and patterns
3. Actionable recommendations for improvement
4. Priority areas for focus
5. Strategic suggestions for optimization

Keep the response concise (2-3 paragraphs) and focused on actionable insights for recruitment teams.
`;

      const systemPrompt = "You are an expert recruitment analytics consultant. Provide strategic insights that help recruitment teams optimize their processes and improve hiring outcomes.";

      const insights = await generateText(prompt, systemPrompt);
      setAiInsights(insights);

      toast({
        title: "AI Insights Generated",
        description: "Dashboard analysis and recommendations are ready."
      });
    } catch (error) {
      console.error('Error generating AI insights:', error);
      toast({
        title: "Insights Unavailable",
        description: "Unable to generate AI insights at the moment.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingInsights(false);
    }
  };

  const formatTrendChange = (trend: number) => {
    const sign = trend >= 0 ? '+' : '';
    return `${sign}${Math.round(trend)}%`;
  };

  const getTrendDirection = (trend: number) => {
    return trend >= 0 ? 'up' : 'down';
  };

  const stats = [
    {
      title: "Total Candidates",
      value: candidates?.length || 0,
      change: formatTrendChange(trendData.candidatesTrend),
      icon: Users,
      trend: getTrendDirection(trendData.candidatesTrend),
    },
    {
      title: "Active Jobs",
      value: jobs?.filter(job => job.is_active).length || 0,
      change: formatTrendChange(trendData.jobsTrend),
      icon: Briefcase,
      trend: getTrendDirection(trendData.jobsTrend),
    },
    {
      title: "This Week",
      value: trendData.weeklyActivity.toString(),
      change: formatTrendChange(trendData.weeklyTrend),
      icon: Calendar,
      trend: getTrendDirection(trendData.weeklyTrend),
    },
    {
      title: "Avg. Score",
      value: candidates?.length
        ? Math.round(candidates.reduce((acc, c) => acc + (c.relationshipScore || 0), 0) / candidates.length) + "%"
        : "0%",
      change: formatTrendChange(trendData.scoreTrend),
      icon: TrendingUp,
      trend: getTrendDirection(trendData.scoreTrend),
    },
  ];

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className={`text-xs ${
                stat.trend === "up" ? "text-green-500" : "text-red-500"
              }`}>
                {stat.change} from last period
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* AI Insights Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              AI Dashboard Insights
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={generateAIInsights}
              disabled={isGeneratingInsights || isLoading}
            >
              {isGeneratingInsights ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate Insights
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        {aiInsights && (
          <CardContent>
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
              <div className="text-sm text-gray-700 whitespace-pre-wrap">
                {aiInsights}
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
